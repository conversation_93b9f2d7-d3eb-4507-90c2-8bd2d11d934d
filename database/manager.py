"""
Database manager for job application automation system.
Handles database operations, migrations, and data management.
"""

import os
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker, Session
from cryptography.fernet import <PERSON>rnet
import json

from .models import (
    Base, UserProfileDB, JobApplicationDB, CredentialDB, 
    SearchHistoryDB, ApplicationAnalyticsDB
)
from models import UserProfile, JobApplication, ApplicationStatus, JobPortalName
from utils.config import config_manager
from utils.logger import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """Manages database operations and provides high-level data access."""
    
    def __init__(self, db_path: Optional[str] = None):
        """Initialize database manager."""
        if db_path is None:
            data_dir = config_manager.get_data_dir()
            db_path = data_dir / "jobflow.db"
        
        self.db_path = str(db_path)
        self.engine = create_engine(f"sqlite:///{self.db_path}", echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Initialize encryption key
        self.encryption_key = self._get_or_create_encryption_key()
        
        # Create tables
        self.init_database()
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for credentials."""
        key_file = config_manager.get_data_dir() / ".encryption_key"
        
        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            # Set restrictive permissions
            os.chmod(key_file, 0o600)
            return key
    
    def init_database(self) -> None:
        """Initialize database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get database session."""
        return self.SessionLocal()
    
    # User Profile Operations
    def save_user_profile(self, profile: UserProfile) -> UserProfileDB:
        """Save or update user profile."""
        with self.get_session() as session:
            try:
                # Check if profile exists
                existing = session.query(UserProfileDB).filter_by(email=profile.email).first()
                
                if existing:
                    # Update existing profile
                    for key, value in profile.model_dump().items():
                        if hasattr(existing, key):
                            setattr(existing, key, value)
                    existing.updated_at = datetime.utcnow()
                    db_profile = existing
                else:
                    # Create new profile
                    db_profile = UserProfileDB(**profile.model_dump())
                    session.add(db_profile)
                
                session.commit()
                session.refresh(db_profile)
                logger.info(f"Saved user profile for {profile.email}")
                return db_profile
                
            except Exception as e:
                session.rollback()
                logger.error(f"Failed to save user profile: {e}")
                raise
    
    def get_user_profile(self, email: str) -> Optional[UserProfile]:
        """Get user profile by email."""
        with self.get_session() as session:
            try:
                db_profile = session.query(UserProfileDB).filter_by(email=email).first()
                if db_profile:
                    profile_data = {
                        'name': db_profile.name,
                        'email': db_profile.email,
                        'phone': db_profile.phone,
                        'skills': db_profile.skills or [],
                        'experience_level': db_profile.experience_level,
                        'years_of_experience': db_profile.years_of_experience,
                        'preferred_job_types': db_profile.preferred_job_types or [],
                        'preferred_locations': db_profile.preferred_locations or [],
                        'preferred_salary_min': db_profile.preferred_salary_min,
                        'preferred_salary_max': db_profile.preferred_salary_max,
                        'resume_path': db_profile.resume_path,
                        'cover_letter_template': db_profile.cover_letter_template,
                        'created_at': db_profile.created_at,
                        'updated_at': db_profile.updated_at
                    }
                    return UserProfile(**profile_data)
                return None
            except Exception as e:
                logger.error(f"Failed to get user profile: {e}")
                return None
    
    # Job Application Operations
    def save_job_application(self, application: JobApplication, user_email: str) -> JobApplicationDB:
        """Save job application."""
        with self.get_session() as session:
            try:
                # Get user profile
                user_profile = session.query(UserProfileDB).filter_by(email=user_email).first()
                if not user_profile:
                    raise ValueError(f"User profile not found for email: {user_email}")
                
                # Check for duplicate
                existing = session.query(JobApplicationDB).filter_by(
                    job_id=application.job_id,
                    portal_name=application.portal_name.value,
                    user_profile_id=user_profile.id
                ).first()
                
                if existing:
                    logger.warning(f"Duplicate application found for job {application.job_id}")
                    return existing
                
                # Create new application
                app_data = application.model_dump()
                app_data['portal_name'] = application.portal_name.value
                app_data['status'] = application.status.value
                app_data['user_profile_id'] = user_profile.id
                
                db_application = JobApplicationDB(**app_data)
                session.add(db_application)
                session.commit()
                session.refresh(db_application)
                
                logger.info(f"Saved job application: {application.position_title} at {application.company_name}")
                return db_application
                
            except Exception as e:
                session.rollback()
                logger.error(f"Failed to save job application: {e}")
                raise
    
    def get_job_applications(self, user_email: str, status: Optional[str] = None, 
                           limit: int = 100) -> List[JobApplication]:
        """Get job applications for user."""
        with self.get_session() as session:
            try:
                query = session.query(JobApplicationDB).join(UserProfileDB).filter(
                    UserProfileDB.email == user_email
                )
                
                if status:
                    query = query.filter(JobApplicationDB.status == status)
                
                query = query.order_by(JobApplicationDB.created_at.desc()).limit(limit)
                db_applications = query.all()
                
                applications = []
                for db_app in db_applications:
                    app_data = {
                        'id': db_app.id,
                        'job_id': db_app.job_id,
                        'portal_name': JobPortalName(db_app.portal_name),
                        'position_title': db_app.position_title,
                        'company_name': db_app.company_name,
                        'job_url': db_app.job_url,
                        'status': ApplicationStatus(db_app.status),
                        'submission_timestamp': db_app.submission_timestamp,
                        'custom_cover_letter': db_app.custom_cover_letter,
                        'created_at': db_app.created_at,
                        'updated_at': db_app.updated_at,
                        'metadata': db_app.job_metadata or {}
                    }
                    applications.append(JobApplication(**app_data))
                
                return applications
                
            except Exception as e:
                logger.error(f"Failed to get job applications: {e}")
                return []
    
    # Credential Operations
    def save_credential(self, user_email: str, service_name: str, 
                       credential_type: str, credential_data: Dict[str, Any]) -> bool:
        """Save encrypted credential."""
        with self.get_session() as session:
            try:
                # Get user profile
                user_profile = session.query(UserProfileDB).filter_by(email=user_email).first()
                if not user_profile:
                    raise ValueError(f"User profile not found for email: {user_email}")
                
                # Check if credential exists
                existing = session.query(CredentialDB).filter_by(
                    user_profile_id=user_profile.id,
                    service_name=service_name
                ).first()
                
                if existing:
                    # Update existing credential
                    existing.encrypt_data(credential_data, self.encryption_key)
                    existing.credential_type = credential_type
                    existing.updated_at = datetime.utcnow()
                    existing.is_validated = False  # Reset validation status
                    credential = existing
                else:
                    # Create new credential
                    credential = CredentialDB(
                        user_profile_id=user_profile.id,
                        service_name=service_name,
                        credential_type=credential_type
                    )
                    credential.encrypt_data(credential_data, self.encryption_key)
                    session.add(credential)
                
                session.commit()
                logger.info(f"Saved credential for {service_name}")
                return True
                
            except Exception as e:
                session.rollback()
                logger.error(f"Failed to save credential: {e}")
                return False
    
    def get_credential(self, user_email: str, service_name: str) -> Optional[Dict[str, Any]]:
        """Get decrypted credential."""
        with self.get_session() as session:
            try:
                credential = session.query(CredentialDB).join(UserProfileDB).filter(
                    UserProfileDB.email == user_email,
                    CredentialDB.service_name == service_name
                ).first()
                
                if credential:
                    return credential.decrypt_data(self.encryption_key)
                return None
                
            except Exception as e:
                logger.error(f"Failed to get credential: {e}")
                return None
    
    # Export Operations
    def export_applications_to_csv(self, user_email: str, file_path: str) -> bool:
        """Export job applications to CSV."""
        try:
            applications = self.get_job_applications(user_email)
            
            if not applications:
                logger.warning("No applications to export")
                return False
            
            # Convert to DataFrame
            data = []
            for app in applications:
                data.append({
                    'Position': app.position_title,
                    'Company': app.company_name,
                    'Portal': app.portal_name.value,
                    'Status': app.status.value,
                    'Applied Date': app.submission_timestamp,
                    'Created Date': app.created_at,
                    'Job URL': app.job_url
                })
            
            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False)
            
            logger.info(f"Exported {len(applications)} applications to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export applications: {e}")
            return False
    
    # Analytics Operations
    def get_application_stats(self, user_email: str, days: int = 30) -> Dict[str, Any]:
        """Get application statistics."""
        with self.get_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Get applications in date range
                applications = session.query(JobApplicationDB).join(UserProfileDB).filter(
                    UserProfileDB.email == user_email,
                    JobApplicationDB.created_at >= cutoff_date
                ).all()
                
                # Calculate stats
                total_applications = len(applications)
                status_counts = {}
                portal_counts = {}
                
                for app in applications:
                    status_counts[app.status] = status_counts.get(app.status, 0) + 1
                    portal_counts[app.portal_name] = portal_counts.get(app.portal_name, 0) + 1
                
                # Calculate rates
                interviews = status_counts.get('interview', 0)
                offers = status_counts.get('offer', 0)
                
                interview_rate = (interviews / total_applications * 100) if total_applications > 0 else 0
                offer_rate = (offers / total_applications * 100) if total_applications > 0 else 0
                
                return {
                    'total_applications': total_applications,
                    'interview_rate': round(interview_rate, 2),
                    'offer_rate': round(offer_rate, 2),
                    'status_breakdown': status_counts,
                    'portal_breakdown': portal_counts,
                    'period_days': days
                }
                
            except Exception as e:
                logger.error(f"Failed to get application stats: {e}")
                return {}


# Global database manager instance
db_manager = DatabaseManager()
