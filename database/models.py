"""
SQLAlchemy database models for job application automation system.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, 
    JSON, Float, ForeignKey, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from cryptography.fernet import Fernet
import json
import os

Base = declarative_base()


class UserProfileDB(Base):
    """User profile database model."""
    __tablename__ = 'user_profiles'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    email = Column(String(255), nullable=False, unique=True)
    phone = Column(String(20), nullable=True)
    
    # Skills and experience
    skills = Column(JSON, default=list)
    experience_level = Column(String(20), default='mid')
    years_of_experience = Column(Integer, default=0)
    
    # Job preferences
    preferred_job_types = Column(JSON, default=list)
    preferred_locations = Column(JSON, default=list)
    preferred_salary_min = Column(Integer, nullable=True)
    preferred_salary_max = Column(Integer, nullable=True)
    
    # Resume and cover letter
    resume_path = Column(String(500), nullable=True)
    cover_letter_template = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    applications = relationship("JobApplicationDB", back_populates="user_profile")
    credentials = relationship("CredentialDB", back_populates="user_profile")
    search_history = relationship("SearchHistoryDB", back_populates="user_profile")


class JobApplicationDB(Base):
    """Job application database model."""
    __tablename__ = 'job_applications'
    
    id = Column(Integer, primary_key=True)
    job_id = Column(String(100), nullable=False)
    portal_name = Column(String(50), nullable=False)
    
    # Job details
    position_title = Column(String(200), nullable=False)
    company_name = Column(String(100), nullable=False)
    job_url = Column(String(1000), nullable=False)
    job_description = Column(Text, nullable=True)
    location = Column(String(100), nullable=True)
    salary_range = Column(String(50), nullable=True)
    
    # Application details
    status = Column(String(20), default='pending')
    submission_timestamp = Column(DateTime, nullable=True)
    custom_cover_letter = Column(Text, nullable=True)
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Additional metadata
    job_metadata = Column(JSON, default=dict)
    
    # Foreign keys
    user_profile_id = Column(Integer, ForeignKey('user_profiles.id'), nullable=False)
    
    # Relationships
    user_profile = relationship("UserProfileDB", back_populates="applications")
    
    # Indexes
    __table_args__ = (
        Index('idx_job_portal', 'job_id', 'portal_name'),
        Index('idx_status_created', 'status', 'created_at'),
        Index('idx_company_position', 'company_name', 'position_title'),
    )


class CredentialDB(Base):
    """Encrypted credentials storage."""
    __tablename__ = 'credentials'
    
    id = Column(Integer, primary_key=True)
    service_name = Column(String(50), nullable=False)  # e.g., 'linkedin', 'indeed', 'openai'
    credential_type = Column(String(20), nullable=False)  # 'api_key', 'oauth', 'username_password'
    
    # Encrypted credential data
    encrypted_data = Column(Text, nullable=False)
    
    # Validation status
    is_validated = Column(Boolean, default=False)
    last_validated = Column(DateTime, nullable=True)
    validation_error = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    user_profile_id = Column(Integer, ForeignKey('user_profiles.id'), nullable=False)
    
    # Relationships
    user_profile = relationship("UserProfileDB", back_populates="credentials")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_profile_id', 'service_name', name='unique_user_service'),
    )
    
    def encrypt_data(self, data: Dict[str, Any], encryption_key: bytes) -> None:
        """Encrypt credential data."""
        fernet = Fernet(encryption_key)
        json_data = json.dumps(data)
        self.encrypted_data = fernet.encrypt(json_data.encode()).decode()
    
    def decrypt_data(self, encryption_key: bytes) -> Dict[str, Any]:
        """Decrypt credential data."""
        fernet = Fernet(encryption_key)
        decrypted_bytes = fernet.decrypt(self.encrypted_data.encode())
        return json.loads(decrypted_bytes.decode())


class SearchHistoryDB(Base):
    """Search history and saved searches."""
    __tablename__ = 'search_history'
    
    id = Column(Integer, primary_key=True)
    search_type = Column(String(20), nullable=False)  # 'job_search', 'saved_search'
    
    # Search parameters
    keywords = Column(JSON, default=list)
    location = Column(String(100), nullable=True)
    portals = Column(JSON, default=list)
    filters = Column(JSON, default=dict)
    
    # Results
    results_count = Column(Integer, default=0)
    execution_time = Column(Float, nullable=True)  # in seconds
    
    # Saved search specific
    search_name = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    alert_frequency = Column(String(20), nullable=True)  # 'daily', 'weekly', 'immediate'
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    last_executed = Column(DateTime, default=datetime.utcnow)
    
    # Foreign keys
    user_profile_id = Column(Integer, ForeignKey('user_profiles.id'), nullable=False)
    
    # Relationships
    user_profile = relationship("UserProfileDB", back_populates="search_history")
    
    # Indexes
    __table_args__ = (
        Index('idx_search_type_created', 'search_type', 'created_at'),
        Index('idx_user_active_searches', 'user_profile_id', 'is_active'),
    )


class ApplicationAnalyticsDB(Base):
    """Analytics and metrics for job applications."""
    __tablename__ = 'application_analytics'
    
    id = Column(Integer, primary_key=True)
    date = Column(DateTime, nullable=False)
    
    # Daily metrics
    applications_submitted = Column(Integer, default=0)
    interviews_scheduled = Column(Integer, default=0)
    offers_received = Column(Integer, default=0)
    rejections_received = Column(Integer, default=0)
    
    # Portal-specific metrics
    portal_stats = Column(JSON, default=dict)  # {portal_name: {submitted: X, success_rate: Y}}
    
    # Performance metrics
    response_rate = Column(Float, default=0.0)
    interview_rate = Column(Float, default=0.0)
    offer_rate = Column(Float, default=0.0)
    
    # Foreign keys
    user_profile_id = Column(Integer, ForeignKey('user_profiles.id'), nullable=False)
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_profile_id', 'date', name='unique_user_date_analytics'),
        Index('idx_user_date', 'user_profile_id', 'date'),
    )
