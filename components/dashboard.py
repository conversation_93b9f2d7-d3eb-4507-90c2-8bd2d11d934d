"""
Dashboard components for job application automation system.
Provides analytics, metrics, and overview with modern UI.
"""

import streamlit as st
import streamlit_antd_components as sac
import streamlit_shadcn_ui as ui
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from database.manager import db_manager
from models import ApplicationStatus, JobPortalName
from utils.logger import get_logger

logger = get_logger(__name__)


class Dashboard:
    """Main dashboard with analytics and metrics."""
    
    def __init__(self, user_email: str):
        self.user_email = user_email
    
    def render(self):
        """Render the main dashboard."""
        st.header("📊 JobFlow Dashboard")
        
        # Get application statistics
        stats = db_manager.get_application_stats(self.user_email, days=30)
        
        if not stats or stats.get('total_applications', 0) == 0:
            self._render_empty_state()
            return
        
        # Render metrics cards
        self._render_metrics_cards(stats)
        
        # Render charts
        col1, col2 = st.columns(2)
        with col1:
            self._render_status_chart(stats)
        with col2:
            self._render_portal_chart(stats)
        
        # Render recent applications
        self._render_recent_applications()
        
        # Render export options
        self._render_export_section()
    
    def _render_empty_state(self):
        """Render empty state when no applications exist."""
        st.markdown("""
        <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
            <h3>🚀 Ready to Start Your Job Search?</h3>
            <p style="font-size: 1.1em; color: #666; margin-bottom: 2rem;">
                You haven't submitted any applications yet. Let's get started!
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🔍 Start Job Search", use_container_width=True, type="primary"):
                st.session_state.current_page = "Job Search"
                st.rerun()
    
    def _render_metrics_cards(self, stats: Dict[str, Any]):
        """Render metrics cards."""
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            ui.metric_card(
                title="Total Applications",
                content=str(stats.get('total_applications', 0)),
                description="Last 30 days",
                key="total_apps_metric"
            )
        
        with col2:
            ui.metric_card(
                title="Interview Rate",
                content=f"{stats.get('interview_rate', 0):.1f}%",
                description="Applications to interviews",
                key="interview_rate_metric"
            )
        
        with col3:
            ui.metric_card(
                title="Offer Rate",
                content=f"{stats.get('offer_rate', 0):.1f}%",
                description="Applications to offers",
                key="offer_rate_metric"
            )
        
        with col4:
            # Calculate average applications per day
            avg_per_day = stats.get('total_applications', 0) / 30
            ui.metric_card(
                title="Daily Average",
                content=f"{avg_per_day:.1f}",
                description="Applications per day",
                key="daily_avg_metric"
            )
    
    def _render_status_chart(self, stats: Dict[str, Any]):
        """Render application status breakdown chart."""
        st.subheader("📈 Application Status")
        
        status_data = stats.get('status_breakdown', {})
        if not status_data:
            st.info("No status data available")
            return
        
        # Create pie chart
        labels = list(status_data.keys())
        values = list(status_data.values())
        
        fig = px.pie(
            values=values,
            names=labels,
            title="Application Status Distribution",
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        
        fig.update_traces(textposition='inside', textinfo='percent+label')
        fig.update_layout(
            showlegend=True,
            height=400,
            font=dict(size=12)
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_portal_chart(self, stats: Dict[str, Any]):
        """Render job portal breakdown chart."""
        st.subheader("🌐 Portal Performance")
        
        portal_data = stats.get('portal_breakdown', {})
        if not portal_data:
            st.info("No portal data available")
            return
        
        # Create bar chart
        portals = list(portal_data.keys())
        counts = list(portal_data.values())
        
        fig = px.bar(
            x=portals,
            y=counts,
            title="Applications by Portal",
            color=counts,
            color_continuous_scale="viridis"
        )
        
        fig.update_layout(
            xaxis_title="Job Portal",
            yaxis_title="Applications",
            height=400,
            showlegend=False
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_recent_applications(self):
        """Render recent applications table."""
        st.subheader("📋 Recent Applications")
        
        # Get recent applications
        applications = db_manager.get_job_applications(self.user_email, limit=10)
        
        if not applications:
            st.info("No applications found")
            return
        
        # Create DataFrame
        data = []
        for app in applications:
            data.append({
                'Position': app.position_title,
                'Company': app.company_name,
                'Portal': app.portal_name.value,
                'Status': app.status.value,
                'Applied': app.submission_timestamp.strftime('%Y-%m-%d') if app.submission_timestamp else 'Not submitted',
                'Created': app.created_at.strftime('%Y-%m-%d %H:%M')
            })
        
        df = pd.DataFrame(data)
        
        # Display with styling
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            column_config={
                'Position': st.column_config.TextColumn('Position', width='medium'),
                'Company': st.column_config.TextColumn('Company', width='medium'),
                'Portal': st.column_config.TextColumn('Portal', width='small'),
                'Status': st.column_config.TextColumn('Status', width='small'),
                'Applied': st.column_config.TextColumn('Applied', width='small'),
                'Created': st.column_config.TextColumn('Created', width='small')
            }
        )
        
        # Show more button
        if len(applications) >= 10:
            if st.button("View All Applications"):
                st.session_state.current_page = "Applications"
                st.rerun()
    
    def _render_export_section(self):
        """Render data export options."""
        st.subheader("📤 Export Data")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📊 Export to CSV", use_container_width=True):
                self._export_to_csv()
        
        with col2:
            if st.button("📋 Export to JSON", use_container_width=True):
                self._export_to_json()
        
        with col3:
            if st.button("📄 Generate Report", use_container_width=True):
                self._generate_pdf_report()
    
    def _export_to_csv(self):
        """Export applications to CSV."""
        try:
            from io import StringIO
            import tempfile
            import os
            
            # Get all applications
            applications = db_manager.get_job_applications(self.user_email, limit=1000)
            
            if not applications:
                st.warning("No applications to export")
                return
            
            # Create CSV data
            data = []
            for app in applications:
                data.append({
                    'Position': app.position_title,
                    'Company': app.company_name,
                    'Portal': app.portal_name.value,
                    'Status': app.status.value,
                    'Job URL': app.job_url,
                    'Applied Date': app.submission_timestamp.strftime('%Y-%m-%d %H:%M') if app.submission_timestamp else '',
                    'Created Date': app.created_at.strftime('%Y-%m-%d %H:%M'),
                    'Updated Date': app.updated_at.strftime('%Y-%m-%d %H:%M')
                })
            
            df = pd.DataFrame(data)
            csv_data = df.to_csv(index=False)
            
            # Offer download
            st.download_button(
                label="📥 Download CSV",
                data=csv_data,
                file_name=f"jobflow_applications_{datetime.now().strftime('%Y%m%d')}.csv",
                mime="text/csv"
            )
            
            st.success(f"✅ Ready to download {len(applications)} applications as CSV")
            
        except Exception as e:
            st.error(f"Failed to export CSV: {e}")
            logger.error(f"CSV export failed: {e}")
    
    def _export_to_json(self):
        """Export applications to JSON."""
        try:
            import json
            
            # Get all applications
            applications = db_manager.get_job_applications(self.user_email, limit=1000)
            
            if not applications:
                st.warning("No applications to export")
                return
            
            # Convert to JSON-serializable format
            data = []
            for app in applications:
                app_dict = app.model_dump()
                # Convert enums and datetime to strings
                app_dict['portal_name'] = app.portal_name.value
                app_dict['status'] = app.status.value
                app_dict['created_at'] = app.created_at.isoformat()
                app_dict['updated_at'] = app.updated_at.isoformat()
                if app.submission_timestamp:
                    app_dict['submission_timestamp'] = app.submission_timestamp.isoformat()
                data.append(app_dict)
            
            json_data = json.dumps(data, indent=2)
            
            # Offer download
            st.download_button(
                label="📥 Download JSON",
                data=json_data,
                file_name=f"jobflow_applications_{datetime.now().strftime('%Y%m%d')}.json",
                mime="application/json"
            )
            
            st.success(f"✅ Ready to download {len(applications)} applications as JSON")
            
        except Exception as e:
            st.error(f"Failed to export JSON: {e}")
            logger.error(f"JSON export failed: {e}")
    
    def _generate_pdf_report(self):
        """Generate PDF report."""
        st.info("📄 PDF report generation coming soon!")
        
        # TODO: Implement PDF report generation
        # This would create a comprehensive report with:
        # - Summary statistics
        # - Charts and graphs
        # - Application timeline
        # - Success metrics
        # - Recommendations
    
    def render_analytics_page(self):
        """Render detailed analytics page."""
        st.header("📈 Detailed Analytics")
        
        # Time range selector
        time_range = st.selectbox(
            "Time Range",
            options=["Last 7 days", "Last 30 days", "Last 90 days", "All time"],
            index=1
        )
        
        days_map = {
            "Last 7 days": 7,
            "Last 30 days": 30,
            "Last 90 days": 90,
            "All time": 365 * 10  # Large number for all time
        }
        
        days = days_map[time_range]
        stats = db_manager.get_application_stats(self.user_email, days=days)
        
        if not stats or stats.get('total_applications', 0) == 0:
            st.info(f"No applications found for {time_range.lower()}")
            return
        
        # Detailed metrics
        self._render_detailed_metrics(stats, time_range)
        
        # Trend analysis
        self._render_trend_analysis(days)
        
        # Success factors
        self._render_success_factors(stats)
    
    def _render_detailed_metrics(self, stats: Dict[str, Any], time_range: str):
        """Render detailed metrics section."""
        st.subheader(f"📊 Metrics for {time_range}")
        
        col1, col2 = st.columns(2)
        
        with col1:
            ui.card(
                content=f"""
                ### Application Summary
                
                **Total Applications**: {stats.get('total_applications', 0)}  
                **Interview Rate**: {stats.get('interview_rate', 0):.1f}%  
                **Offer Rate**: {stats.get('offer_rate', 0):.1f}%  
                **Period**: {time_range}  
                """,
                key="detailed_summary"
            )
        
        with col2:
            # Calculate additional metrics
            total_apps = stats.get('total_applications', 0)
            interviews = sum(1 for status, count in stats.get('status_breakdown', {}).items() 
                           if 'interview' in status.lower()) if stats.get('status_breakdown') else 0
            
            ui.card(
                content=f"""
                ### Performance Insights
                
                **Response Rate**: {(interviews / total_apps * 100) if total_apps > 0 else 0:.1f}%  
                **Applications/Day**: {total_apps / stats.get('period_days', 30):.1f}  
                **Top Portal**: {max(stats.get('portal_breakdown', {}), key=stats.get('portal_breakdown', {}).get, default='N/A')}  
                **Success Score**: {stats.get('offer_rate', 0) * 10:.0f}/100  
                """,
                key="performance_insights"
            )
    
    def _render_trend_analysis(self, days: int):
        """Render trend analysis charts."""
        st.subheader("📈 Trend Analysis")
        st.info("Trend analysis charts coming soon!")
        
        # TODO: Implement trend analysis
        # This would show:
        # - Applications over time
        # - Success rate trends
        # - Portal performance over time
        # - Seasonal patterns
    
    def _render_success_factors(self, stats: Dict[str, Any]):
        """Render success factors analysis."""
        st.subheader("🎯 Success Factors")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 📊 Portal Performance")
            portal_data = stats.get('portal_breakdown', {})
            if portal_data:
                for portal, count in sorted(portal_data.items(), key=lambda x: x[1], reverse=True):
                    percentage = (count / stats.get('total_applications', 1)) * 100
                    st.write(f"**{portal}**: {count} applications ({percentage:.1f}%)")
            else:
                st.info("No portal data available")
        
        with col2:
            st.markdown("#### 🎯 Recommendations")
            
            total_apps = stats.get('total_applications', 0)
            interview_rate = stats.get('interview_rate', 0)
            offer_rate = stats.get('offer_rate', 0)
            
            recommendations = []
            
            if total_apps < 10:
                recommendations.append("📈 Increase application volume for better results")
            
            if interview_rate < 10:
                recommendations.append("📝 Consider improving your resume and cover letter")
            
            if offer_rate < 5:
                recommendations.append("🎯 Focus on better job matching and preparation")
            
            if not recommendations:
                recommendations.append("🎉 Great job! Keep up the excellent work!")
            
            for rec in recommendations:
                st.write(f"• {rec}")
