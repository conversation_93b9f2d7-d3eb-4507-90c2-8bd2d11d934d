"""
Onboarding flow components for job application automation system.
Provides a beautiful 5-step setup process with modern UI.
"""

import streamlit as st
import streamlit_antd_components as sac
import streamlit_shadcn_ui as ui
from typing import Dict, Any, Optional
from pathlib import Path

from datetime import datetime
from models import UserProfile, ExperienceLevel
from database.manager import db_manager
from utils.config import config_manager
from utils.logger import get_logger

logger = get_logger(__name__)


class OnboardingFlow:
    """Manages the 5-step onboarding process."""
    
    def __init__(self):
        self.steps = [
            {"title": "Welcome", "icon": "🚀", "description": "Privacy & Introduction"},
            {"title": "Profile", "icon": "👤", "description": "Personal Information"},
            {"title": "Credentials", "icon": "🔐", "description": "API Keys & Access"},
            {"title": "Portals", "icon": "🌐", "description": "Job Portal Setup"},
            {"title": "Complete", "icon": "✅", "description": "Final Configuration"}
        ]
    
    def render(self) -> bool:
        """Render the onboarding flow. Returns True if completed."""
        # Initialize session state
        if 'onboarding_step' not in st.session_state:
            st.session_state.onboarding_step = 0
        if 'onboarding_data' not in st.session_state:
            st.session_state.onboarding_data = {}
        
        # Render progress indicator
        self._render_progress()
        
        # Render current step
        current_step = st.session_state.onboarding_step
        
        if current_step == 0:
            return self._render_welcome()
        elif current_step == 1:
            return self._render_profile_setup()
        elif current_step == 2:
            return self._render_credentials_setup()
        elif current_step == 3:
            return self._render_portals_setup()
        elif current_step == 4:
            return self._render_completion()
        
        return False
    
    def _render_progress(self):
        """Render the progress indicator."""
        current_step = st.session_state.onboarding_step
        
        # Create progress steps
        steps_data = []
        for i, step in enumerate(self.steps):
            status = "finish" if i < current_step else ("process" if i == current_step else "wait")
            steps_data.append({
                "title": step["title"],
                "description": step["description"],
                "status": status
            })
        
        # Render with antd components
        sac.steps(
            items=steps_data,
            index=current_step,
            format_func='title',
            size='default'
        )
        
        st.markdown("---")
    
    def _render_welcome(self) -> bool:
        """Render welcome step."""
        st.markdown("""
        <div style="text-align: center; padding: 2rem;">
            <h1>🚀 Welcome to JobFlow</h1>
            <h3>Smart Job Application Automation</h3>
            <p style="font-size: 1.2em; color: #666;">
                Let's get you set up in just 5 simple steps
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Privacy-first messaging
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            ui.card(
                content="""
                ### 🔒 Privacy First
                
                - **Local Storage**: All your data stays on your device
                - **No Cloud Sync**: We never upload your personal information
                - **Encrypted Credentials**: API keys are encrypted locally
                - **Full Control**: Export or delete your data anytime
                
                ### ✨ What You'll Get
                
                - **Multi-Portal Search**: WeWorkRemotely, Remote.co, FlexJobs & more
                - **Smart Automation**: Intelligent job matching and application
                - **Progress Tracking**: Detailed analytics and success metrics
                - **Export Options**: CSV, JSON, and PDF reports
                """,
                key="privacy_card"
            )
        
        st.markdown("<br>", unsafe_allow_html=True)
        
        # Navigation buttons
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button("🚀 Let's Get Started", use_container_width=True, type="primary"):
                st.session_state.onboarding_step = 1
                st.rerun()
        
        return False
    
    def _render_profile_setup(self) -> bool:
        """Render profile setup step."""
        st.header("👤 Profile Setup")
        st.write("Tell us about yourself to personalize your job search experience.")
        
        with st.form("profile_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Personal Information")
                name = st.text_input(
                    "Full Name *",
                    value=st.session_state.onboarding_data.get('name', ''),
                    placeholder="John Doe"
                )
                email = st.text_input(
                    "Email Address *",
                    value=st.session_state.onboarding_data.get('email', ''),
                    placeholder="<EMAIL>"
                )
                phone = st.text_input(
                    "Phone Number",
                    value=st.session_state.onboarding_data.get('phone', ''),
                    placeholder="+****************"
                )
            
            with col2:
                st.subheader("Experience")
                experience_level = st.selectbox(
                    "Experience Level *",
                    options=[level.value for level in ExperienceLevel],
                    index=list(ExperienceLevel).index(
                        ExperienceLevel(st.session_state.onboarding_data.get('experience_level', 'mid'))
                    )
                )
                years_experience = st.number_input(
                    "Years of Experience *",
                    min_value=0, max_value=50,
                    value=st.session_state.onboarding_data.get('years_of_experience', 0)
                )
            
            st.subheader("Skills & Preferences")
            col3, col4 = st.columns(2)
            
            with col3:
                skills_input = st.text_area(
                    "Skills (one per line) *",
                    value="\n".join(st.session_state.onboarding_data.get('skills', [])),
                    placeholder="Python\nJavaScript\nReact\nNode.js",
                    height=120
                )
                
                job_types_input = st.text_area(
                    "Preferred Job Types (one per line) *",
                    value="\n".join(st.session_state.onboarding_data.get('preferred_job_types', [])),
                    placeholder="Software Engineer\nFull Stack Developer\nBackend Developer",
                    height=100
                )
            
            with col4:
                locations_input = st.text_area(
                    "Preferred Locations (one per line)",
                    value="\n".join(st.session_state.onboarding_data.get('preferred_locations', [])),
                    placeholder="Remote\nNew York, NY\nSan Francisco, CA",
                    height=120
                )
                
                col5, col6 = st.columns(2)
                with col5:
                    salary_min = st.number_input(
                        "Min Salary ($)",
                        min_value=0, step=5000,
                        value=st.session_state.onboarding_data.get('preferred_salary_min', 0)
                    )
                with col6:
                    salary_max = st.number_input(
                        "Max Salary ($)",
                        min_value=0, step=5000,
                        value=st.session_state.onboarding_data.get('preferred_salary_max', 0)
                    )
            
            # Resume upload
            st.subheader("Resume & Cover Letter")
            resume_file = st.file_uploader(
                "Upload Resume (PDF, DOC, DOCX)",
                type=['pdf', 'doc', 'docx'],
                help="Your resume will be stored locally and used for applications"
            )
            
            cover_letter_template = st.text_area(
                "Cover Letter Template",
                value=st.session_state.onboarding_data.get('cover_letter_template', ''),
                placeholder="Dear {company_name} team,\n\nI am excited to apply for the {position_title} position...",
                height=150,
                help="Use {company_name} and {position_title} as placeholders"
            )
            
            # Form submission
            col1, col2, col3 = st.columns([1, 1, 1])
            with col1:
                if st.form_submit_button("← Back", use_container_width=True):
                    st.session_state.onboarding_step = 0
                    st.rerun()
            
            with col3:
                if st.form_submit_button("Next →", use_container_width=True, type="primary"):
                    # Validate required fields
                    if not name or not email or not skills_input or not job_types_input:
                        st.error("Please fill in all required fields marked with *")
                        return False
                    
                    # Process and save data
                    skills = [skill.strip() for skill in skills_input.split('\n') if skill.strip()]
                    job_types = [jt.strip() for jt in job_types_input.split('\n') if jt.strip()]
                    locations = [loc.strip() for loc in locations_input.split('\n') if loc.strip()]
                    
                    # Handle resume upload
                    resume_path = None
                    if resume_file:
                        resume_dir = config_manager.get_data_dir() / "resumes"
                        resume_dir.mkdir(exist_ok=True)
                        resume_path = resume_dir / resume_file.name
                        with open(resume_path, 'wb') as f:
                            f.write(resume_file.getbuffer())
                    
                    # Store in session state
                    st.session_state.onboarding_data.update({
                        'name': name,
                        'email': email,
                        'phone': phone if phone else None,
                        'skills': skills,
                        'experience_level': experience_level,
                        'years_of_experience': years_experience,
                        'preferred_job_types': job_types,
                        'preferred_locations': locations,
                        'preferred_salary_min': salary_min if salary_min > 0 else None,
                        'preferred_salary_max': salary_max if salary_max > 0 else None,
                        'resume_path': str(resume_path) if resume_path else None,
                        'cover_letter_template': cover_letter_template
                    })
                    
                    st.session_state.onboarding_step = 2
                    st.rerun()
        
        return False
    
    def _render_credentials_setup(self) -> bool:
        """Render credentials setup step."""
        st.header("🔐 API Credentials Setup")
        st.write("Configure your API keys and credentials for enhanced job search capabilities.")
        
        # Import credential manager here to avoid circular imports
        from .credentials import CredentialManager
        credential_manager = CredentialManager()
        
        # Render credential setup
        credential_manager.render_setup_form()
        
        # Navigation
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if st.button("← Back", use_container_width=True):
                st.session_state.onboarding_step = 1
                st.rerun()
        
        with col3:
            if st.button("Next →", use_container_width=True, type="primary"):
                st.session_state.onboarding_step = 3
                st.rerun()
        
        return False
    
    def _render_portals_setup(self) -> bool:
        """Render job portals setup step."""
        st.header("🌐 Job Portal Configuration")
        st.write("Choose which job portals to include in your automated search.")
        
        # Portal configuration will be implemented here
        st.info("Portal configuration coming in next update!")
        
        # Navigation
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if st.button("← Back", use_container_width=True):
                st.session_state.onboarding_step = 2
                st.rerun()
        
        with col3:
            if st.button("Complete Setup →", use_container_width=True, type="primary"):
                st.session_state.onboarding_step = 4
                st.rerun()
        
        return False
    
    def _render_completion(self) -> bool:
        """Render completion step."""
        st.header("✅ Setup Complete!")
        st.write("Congratulations! Your JobFlow account is ready to use.")
        
        # Save user profile to database
        try:
            profile_data = st.session_state.onboarding_data
            user_profile = UserProfile(**profile_data)
            db_manager.save_user_profile(user_profile)
            
            # Mark onboarding as complete
            st.session_state.onboarding_complete = True
            
            # Save completion flag
            completion_file = config_manager.get_data_dir() / "onboarding_complete.json"
            with open(completion_file, 'w') as f:
                import json
                json.dump({"completed": True, "date": str(datetime.now())}, f)
            
            st.success("✅ Profile saved successfully!")
            
        except Exception as e:
            st.error(f"Error saving profile: {e}")
            logger.error(f"Failed to save profile during onboarding: {e}")
            return False
        
        # Summary
        col1, col2 = st.columns(2)
        with col1:
            ui.card(
                content=f"""
                ### 📊 Your Profile Summary
                
                **Name**: {st.session_state.onboarding_data.get('name')}  
                **Email**: {st.session_state.onboarding_data.get('email')}  
                **Experience**: {st.session_state.onboarding_data.get('experience_level')} ({st.session_state.onboarding_data.get('years_of_experience')} years)  
                **Skills**: {len(st.session_state.onboarding_data.get('skills', []))} skills added  
                **Job Types**: {len(st.session_state.onboarding_data.get('preferred_job_types', []))} preferences  
                """,
                key="profile_summary"
            )
        
        with col2:
            ui.card(
                content="""
                ### 🚀 Next Steps
                
                1. **Explore Dashboard** - View your application metrics
                2. **Start Job Search** - Find and apply to jobs automatically  
                3. **Track Progress** - Monitor your application success
                4. **Export Data** - Download your application history
                
                Ready to find your dream job? 
                """,
                key="next_steps"
            )
        
        st.markdown("<br>", unsafe_allow_html=True)
        
        # Final button
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button("🚀 Launch JobFlow", use_container_width=True, type="primary"):
                # Clear onboarding data
                del st.session_state.onboarding_data
                del st.session_state.onboarding_step
                st.rerun()
        
        return True
